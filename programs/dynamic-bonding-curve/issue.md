In virtual_pool.rs

Description:

BUY: min_token_amount is compared to unscaled tokens or pre-fee amounts.

SELL: min_sol_output is checked on gross SOL, not net after fee.
This aligns with primer’s “Inaccurate calculation results / loss of precision” patterns.
Exploit Scenario: Users receive less than promised; trades pass slippage they shouldn’t.
Recommendation (patterns):

// BUY: compare scaled, after-fee amount
let out_unscaled = quote_out(...)?;
let out_scaled = out_unscaled.checked_mul(10u64.pow(DECIMALS)).ok_or(Error::Overflow)?;
require!(out_scaled >= min_token_amount, Error::ExcessiveSlippage);

// SELL: compare net after fees
let sol_gross = quote_sol_out(...)?;
let fee = ceil_bps(sol_gross, FEE_BPS);
let sol_net = sol_gross.checked_sub(fee).ok_or(Error::Math)?;
require!(sol_net >= min_sol_output, Error::ExcessiveSlippage);

